/*
 * Automatic Staircase Lighting System
 * Author: Arduino Project
 * Description: Controls LED strips on stairs using 2 PIR sensors
 * Features:
 * - Sequential lighting when person enters from either direction
 * - Sequential turn-off when person exits
 * - 30-second timeout if no exit detected
 * - Ice blue color lighting
 */

#include <FastLED.h>

// Pin definitions
#define PIR_BOTTOM_PIN 2    // PIR sensor at bottom of stairs
#define PIR_TOP_PIN 3       // PIR sensor at top of stairs
#define LED_DATA_PIN 6      // Data pin for LED strip
#define NUM_STEPS 8         // Number of stair steps
#define LEDS_PER_STEP 10    // Number of LEDs per step

// LED configuration
#define NUM_LEDS (NUM_STEPS * LEDS_PER_STEP)
CRGB leds[NUM_LEDS];

// Timing constants
#define STEP_DELAY 200      // Delay between lighting each step (ms)
#define TIMEOUT_DURATION 30000  // 30 seconds timeout (ms)
#define SENSOR_DEBOUNCE 500     // Debounce time for sensors (ms)

// System states
enum SystemState {
  IDLE,
  ASCENDING,
  DESCENDING,
  LIGHTS_ON,
  TURNING_OFF_UP,
  TURNING_OFF_DOWN
};

// Global variables
SystemState currentState = IDLE;
unsigned long lastMotionTime = 0;
unsigned long lastSensorTrigger = 0;
int currentStep = 0;
bool pirBottomState = false;
bool pirTopState = false;
bool pirBottomPrevious = false;
bool pirTopPrevious = false;

// Ice blue color definition
CRGB iceBlue = CRGB(173, 216, 230);  // Light blue/ice blue color

void setup() {
  Serial.begin(9600);
  Serial.println("Automatic Staircase System Starting...");
  
  // Initialize PIR sensors
  pinMode(PIR_BOTTOM_PIN, INPUT);
  pinMode(PIR_TOP_PIN, INPUT);
  
  // Initialize LED strip
  FastLED.addLeds<WS2812B, LED_DATA_PIN, GRB>(leds, NUM_LEDS);
  FastLED.setBrightness(100);  // Adjust brightness (0-255)
  
  // Clear all LEDs
  fill_solid(leds, NUM_LEDS, CRGB::Black);
  FastLED.show();
  
  Serial.println("System initialized. Waiting for motion...");
}

void loop() {
  // Read sensor states with debouncing
  readSensors();
  
  // State machine
  switch (currentState) {
    case IDLE:
      handleIdleState();
      break;
      
    case ASCENDING:
      handleAscendingState();
      break;
      
    case DESCENDING:
      handleDescendingState();
      break;
      
    case LIGHTS_ON:
      handleLightsOnState();
      break;
      
    case TURNING_OFF_UP:
      handleTurningOffUpState();
      break;
      
    case TURNING_OFF_DOWN:
      handleTurningOffDownState();
      break;
  }
  
  delay(50);  // Small delay for stability
}

void readSensors() {
  static unsigned long lastRead = 0;
  
  if (millis() - lastRead > 50) {  // Read every 50ms
    pirBottomPrevious = pirBottomState;
    pirTopPrevious = pirTopState;
    
    pirBottomState = digitalRead(PIR_BOTTOM_PIN);
    pirTopState = digitalRead(PIR_TOP_PIN);
    
    lastRead = millis();
  }
}

void handleIdleState() {
  // Check for motion at bottom sensor (person starting to go up)
  if (pirBottomState && !pirBottomPrevious && 
      millis() - lastSensorTrigger > SENSOR_DEBOUNCE) {
    Serial.println("Motion detected at bottom - Starting ascent");
    currentState = ASCENDING;
    currentStep = 0;
    lastMotionTime = millis();
    lastSensorTrigger = millis();
  }
  
  // Check for motion at top sensor (person starting to go down)
  else if (pirTopState && !pirTopPrevious && 
           millis() - lastSensorTrigger > SENSOR_DEBOUNCE) {
    Serial.println("Motion detected at top - Starting descent");
    currentState = DESCENDING;
    currentStep = NUM_STEPS - 1;
    lastMotionTime = millis();
    lastSensorTrigger = millis();
  }
}

void handleAscendingState() {
  static unsigned long lastStepTime = 0;
  
  if (millis() - lastStepTime > STEP_DELAY) {
    lightUpStep(currentStep);
    Serial.print("Lighting step: ");
    Serial.println(currentStep + 1);
    
    currentStep++;
    lastStepTime = millis();
    
    if (currentStep >= NUM_STEPS) {
      currentState = LIGHTS_ON;
      lastMotionTime = millis();
      Serial.println("All steps lit - Waiting for exit or timeout");
    }
  }
}

void handleDescendingState() {
  static unsigned long lastStepTime = 0;
  
  if (millis() - lastStepTime > STEP_DELAY) {
    lightUpStep(currentStep);
    Serial.print("Lighting step: ");
    Serial.println(currentStep + 1);
    
    currentStep--;
    lastStepTime = millis();
    
    if (currentStep < 0) {
      currentState = LIGHTS_ON;
      lastMotionTime = millis();
      Serial.println("All steps lit - Waiting for exit or timeout");
    }
  }
}

void handleLightsOnState() {
  // Check for exit at top (person came from bottom)
  if (pirTopState && !pirTopPrevious && 
      millis() - lastSensorTrigger > SENSOR_DEBOUNCE) {
    Serial.println("Exit detected at top - Starting turn off sequence");
    currentState = TURNING_OFF_UP;
    currentStep = NUM_STEPS - 1;
    lastSensorTrigger = millis();
  }
  
  // Check for exit at bottom (person came from top)
  else if (pirBottomState && !pirBottomPrevious && 
           millis() - lastSensorTrigger > SENSOR_DEBOUNCE) {
    Serial.println("Exit detected at bottom - Starting turn off sequence");
    currentState = TURNING_OFF_DOWN;
    currentStep = 0;
    lastSensorTrigger = millis();
  }
  
  // Check for timeout
  else if (millis() - lastMotionTime > TIMEOUT_DURATION) {
    Serial.println("Timeout reached - Turning off all lights");
    turnOffAllLights();
    currentState = IDLE;
  }
}

void handleTurningOffUpState() {
  static unsigned long lastStepTime = 0;
  
  if (millis() - lastStepTime > STEP_DELAY) {
    turnOffStep(currentStep);
    Serial.print("Turning off step: ");
    Serial.println(currentStep + 1);
    
    currentStep--;
    lastStepTime = millis();
    
    if (currentStep < 0) {
      currentState = IDLE;
      Serial.println("All lights turned off - System ready");
    }
  }
}

void handleTurningOffDownState() {
  static unsigned long lastStepTime = 0;
  
  if (millis() - lastStepTime > STEP_DELAY) {
    turnOffStep(currentStep);
    Serial.print("Turning off step: ");
    Serial.println(currentStep + 1);
    
    currentStep++;
    lastStepTime = millis();
    
    if (currentStep >= NUM_STEPS) {
      currentState = IDLE;
      Serial.println("All lights turned off - System ready");
    }
  }
}

void lightUpStep(int step) {
  if (step >= 0 && step < NUM_STEPS) {
    int startLED = step * LEDS_PER_STEP;
    int endLED = startLED + LEDS_PER_STEP;
    
    for (int i = startLED; i < endLED; i++) {
      leds[i] = iceBlue;
    }
    FastLED.show();
  }
}

void turnOffStep(int step) {
  if (step >= 0 && step < NUM_STEPS) {
    int startLED = step * LEDS_PER_STEP;
    int endLED = startLED + LEDS_PER_STEP;
    
    for (int i = startLED; i < endLED; i++) {
      leds[i] = CRGB::Black;
    }
    FastLED.show();
  }
}

void turnOffAllLights() {
  fill_solid(leds, NUM_LEDS, CRGB::Black);
  FastLED.show();
}
