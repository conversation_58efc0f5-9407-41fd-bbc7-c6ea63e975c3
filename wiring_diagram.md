# Automatic Staircase System - Wiring Diagram

## Components Required:
- 1x Arduino Nano
- 2x PIR Motion Sensors (HC-SR501)
- 1x WS2812B LED Strip (or similar addressable LED strip)
- 1x 5V Power Supply (adequate for LED strip - calculate based on number of LEDs)
- Jumper wires
- Breadboard or PCB for connections
- Resistors (if needed for level shifting)

## Pin Connections:

### Arduino Nano Connections:
```
Arduino Nano    |    Component
----------------|------------------
D2              |    PIR Bottom Sensor (OUT)
D3              |    PIR Top Sensor (OUT)
D6              |    LED Strip Data Pin
5V              |    PIR Sensors VCC
GND             |    PIR Sensors GND & LED Strip GND
VIN             |    External 5V Power Supply (+)
GND             |    External 5V Power Supply (-)
```

### PIR Sensor Connections:
```
PIR Sensor Pin  |    Arduino Nano
----------------|------------------
VCC             |    5V
GND             |    GND
OUT             |    D2 (Bottom) / D3 (Top)
```

### LED Strip Connections:
```
LED Strip Pin   |    Connection
----------------|------------------
5V/VCC          |    External 5V Power Supply (+)
GND             |    Arduino GND & Power Supply (-)
DIN/Data        |    Arduino D6
```

## Power Considerations:
- Each WS2812B LED can draw up to 60mA at full brightness
- For 80 LEDs (8 steps × 10 LEDs): 80 × 60mA = 4.8A maximum
- Use a 5V 6A power supply for safety margin
- Connect power supply ground to Arduino ground

## Physical Installation:
1. Mount PIR sensors at bottom and top of staircase
2. Install LED strips under each step
3. Place Arduino Nano in a protective enclosure
4. Use appropriate wire gauge for power connections
5. Ensure PIR sensors have clear view of stair area

## Configuration Notes:
- Adjust `NUM_STEPS` in code based on your staircase
- Adjust `LEDS_PER_STEP` based on your LED strip density
- Modify `STEP_DELAY` for faster/slower lighting sequence
- Adjust `TIMEOUT_DURATION` for different timeout periods
- Fine-tune PIR sensor sensitivity using onboard potentiometers

## Safety Notes:
- Use proper electrical enclosures for outdoor installations
- Ensure all connections are secure and insulated
- Test system thoroughly before final installation
- Consider adding fuses for overcurrent protection
